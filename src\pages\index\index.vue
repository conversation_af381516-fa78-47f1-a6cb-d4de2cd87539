<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="wrapper">
    <CustomNavbar title="" />
    <view class="px-4">
      <view class="banner">
        <wd-swiper
          :list="swiperList"
          autoplay
          v-model:current="current"
          @click="handleClick"
          :height="146"
          imageMode="widthFix"
          @change="onChange"
        ></wd-swiper>
      </view>

      <view class="menus">
        <block v-for="(menu, index) in menus" :key="index">
          <view class="menu" @click="handleMenuClick(menu)">
            <image :src="menu.icon" class="icon" mode="widthFix" />
            <view class="name">{{ menu.label }}</view>
          </view>
        </block>
      </view>
    </view>
    <!-- 热门活动板块 -->
    <view class="hot-section">
      <view class="section-header">
        <view class="section-title">热门赛事</view>
        <view class="more-btn" @click="toActivityList">
          <text>更多赛事</text>
          <wd-icon name="arrow-right" size="14"></wd-icon>
        </view>
      </view>
      <view class="activity-list">
        <scroll-view class="scroll-view_H" scroll-x="true" scroll-left="120">
          <view
            v-for="item in hotActivities"
            :key="item.id"
            class="activity-item"
            @click="toActivityDetail(item.id)"
          >
            <view class="activity-image-wrapper">
              <image
                class="activity-image"
                :src="item.image_input?.[0] || '/static/images/banner.png'"
                mode="aspectFill"
              ></image>
              <view class="activity-status">报名倒计时：3 天 23 小时 40 分 18 秒</view>
            </view>
            <view class="activity-info">
              <view class="activity-title">{{ item.title }}</view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    <!-- 位置选择区域 -->
    <view class="location-section">
      <view class="location-header">
        <view class="location-text">
          <text class="location-label">所在地区</text>
          <view class="location-picker" @click="showLocationPicker">
            <text class="location-value">{{ selectedLocation }}</text>
            <wd-icon name="arrow-down" size="14" color="#666"></wd-icon>
          </view>
        </view>
        <view class="location-btn" @click="getCurrentLocation">
          <wd-icon name="location" size="16" color="#666"></wd-icon>
          <text class="location-btn-text">定位</text>
        </view>
      </view>
    </view>
    <!-- 拼房拼车板块 -->
    <view class="companion-section">
      <view class="companion-tabs">
        <view
          class="tab"
          :class="{ active: companionTab === 'house' }"
          @click="switchCompanionTab('house')"
        >
          拼房搭子
        </view>
        <view
          class="tab"
          :class="{ active: companionTab === 'car' }"
          @click="switchCompanionTab('car')"
        >
          拼车搭子
        </view>
        <view
          class="tab"
          :class="{ active: companionTab === 'pace' }"
          @click="switchCompanionTab('pace')"
        >
          私兔陪跑
        </view>
      </view>
      <view
        class="companion-card"
        v-for="item in displayedCompanionList"
        :key="item.id"
        @click="toCompanionDetail(item.id)"
      >
        <view class="card-header">
          <view class="user-info">
            <image
              class="user-avatar"
              :src="item.user?.avatar || '/static/images/login-female-default.png'"
              mode="aspectFill"
            ></image>
            <view class="user-details">
              <text class="user-name">{{ item.user?.nickname || '美丽人生' }}</text>
              <view class="user-location">
                <image src="/static/images/icon-qizhi.png" class="icon" mode="scaleToFill" />
                <text class="location-text">{{ item.location || '天府跑团' }}</text>
              </view>
            </view>
          </view>
          <view class="post-btn-small" @click.stop="toPost">
            <wd-icon name="add" size="12" color="#333"></wd-icon>
            <text class="post-text">发布</text>
          </view>
        </view>

        <view class="card-content">
          <view class="content-title">{{ item.title }}</view>
          <view class="flex items-center">
            <image
              src="https://dummyimage.com/148x148/3c9cff/fff"
              mode="scaleToFill"
              class="avatar"
            />
            <view class="">
              <view class="content-tags">
                <view class="tag price-tag">{{ item.price }}</view>
                <view class="tag type-tag">{{ item.type }}</view>
                <view class="tag gender-tag">{{ item.gender }}</view>
              </view>
              <view class="content-info">
                <view class="info-item">
                  <image src="/static/images/icon-saishi.png" class="icon" mode="scaleToFill" />
                  <text class="info-text">{{ item.event_name }}</text>
                </view>
                <view class="info-item">
                  <image src="/static/images/icon-time-2.png" class="icon" mode="scaleToFill" />
                  <text class="info-text">{{ item.date_time }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 发帖按钮 -->
    <button hover-class="button-hover" class="post-btn" @click="toPost">
      <wd-icon name="add" size="12"></wd-icon>
      发帖
    </button>
    <wd-popup v-model="postPopupShow" position="top" custom-style="border-radius:32rpx;" @close="handleClose">
      <text class="custom-txt">弹弹弹</text>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { indexUser } from '@/service/user'
import { getBanners } from '@/service/public/index'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import CustomNavbar from '@/components/CustomNavbar/index.vue'

//
defineOptions({
  name: 'Home',
})
const duration = ref(500)
const current = ref(0)
const postPopupShow = ref(false)

const { onShareAppMessage, onShareTimeline } = useShare()
const userStore = useUserStore()
const { isComplete } = storeToRefs(userStore)
const pageData = reactive({
  cityId: '',
  nickname: '',
  page: 1,
  limit: 10,
  loading: false,
  loadend: false,
})
const self = reactive<{
  userList: any[]
  current: number
}>({
  userList: [],
  current: 0,
})
const menus = [
  {
    icon: '/static/images/icon-menu-1.png',
    label: '赛事日历',
    url: '',
  },
  {
    icon: '/static/images/icon-menu-2.png',
    label: '拼房搭子',
    url: '',
  },
  {
    icon: '/static/images/icon-menu-3.png',
    label: '拼车搭子',
    url: '',
  },
  {
    icon: '/static/images/icon-menu-4.png',
    label: '私兔陪跑',
    url: '',
  },
  {
    icon: '/static/images/icon-menu-5.png',
    label: '社区交流',
    url: '',
  },
]
const handleClose = (e) => {
  console.log('🚀 ~ handleClose ~ e:', e)
}

const handleCityChange = (e) => {
  pageData.cityId = e
  reset()
}
const handleSearch = (e) => {
  pageData.nickname = e
  reset()
}
const reset = () => {
  pageData.page = 1
  pageData.loadend = false
  self.userList = []
  loadUserList()
}

// 加载首页推荐人员
const loadUserList = async () => {
  if (pageData.loading || pageData.loadend) return
  pageData.loading = true

  const data = {
    city_id: pageData.cityId,
    nickname: pageData.nickname,
    page: pageData.page,
    limit: pageData.limit,
  }
  const [res, err] = await indexUser(data)
  if (res) {
    res.data.forEach((element) => {
      if (element.life_img) {
        element.coverImg = element.life_img.split(',')[0]
      } else {
        element.coverImg = '/static/images/home-indo.png'
      }
      if (element.cards) {
        element.cardsImg = element.cards.split(',')
      } else {
        element.cardsImg = []
      }
    })
    const list = res.data
    const loadEnd = list.length < pageData.limit
    pageData.loadend = loadEnd
    pageData.page++
    self.userList = [...self.userList, ...list]
  }
  pageData.loading = false
}
// 跳转到用户详情
const toUserDetail = (item) => {
  uni.navigateTo({
    url: '/pages/users/profile-detail?uid=' + item.uid, // 跳转的页面
  })
}

const swiperList = ref(['/static/images/banner.png'])
const hotActivities = ref([])
const hotTopics = ref([])

// 位置选择相关
const selectedLocation = ref('清先泽省/市区')
const showLocationPicker = () => {
  // 显示位置选择器
  console.log('显示位置选择器')
}

const getCurrentLocation = () => {
  // 获取当前位置
  console.log('获取当前位置')
}

// 拼房拼车数据与Tab
const companionTab = ref<'house' | 'car' | 'pace'>('house')
const companionList = ref<any[]>([])
const displayedCompanionList = computed(() =>
  companionList.value.filter((it) => it.typeKey === companionTab.value),
)
const switchCompanionTab = (key: 'house' | 'car' | 'pace') => {
  companionTab.value = key
}

// 菜单点击事件
const handleMenuClick = (menu) => {
  console.log('点击菜单:', menu.label)
  // 根据菜单类型跳转到对应页面
  if (menu.url) {
    uni.navigateTo({
      url: menu.url,
    })
  }
}

// 加载拼房拼车数据
const loadCompanionList = async () => {
  // 使用测试数据
  companionList.value = [
    {
      id: 1,
      title: '预算100-150找10月25日成马起点附近拼房',
      price: '60',
      type: '拼房',
      typeKey: 'house',
      gender: '限男',
      event_name: '2025大邑安仁半程马拉松',
      date_time: '25/07/19 12:00:00—25/07/19 12:00:00',
      location: '天府跑团',
      user: {
        nickname: '美丽人生',
        avatar: '/static/images/login-female-default.png',
      },
    },
    {
      id: 2,
      title: '9月15日去比赛，成马赛道沿线求拼车',
      price: 'AA',
      type: '拼车',
      typeKey: 'car',
      gender: '不限',
      event_name: '2025重庆(长嘉汇)半程马拉松',
      date_time: '25/09/15 06:30—25/09/15 12:00',
      location: '南门—比赛起点',
      user: {
        nickname: '跑在成都',
        avatar: '/static/images/login-male-default.png',
      },
    },
    {
      id: 3,
      title: '周末早上天府绿道10公里配速5:30，求陪跑',
      price: '免费',
      type: '私兔',
      typeKey: 'pace',
      gender: '不限',
      event_name: '天府绿道训练',
      date_time: '每周六 7:00—8:30',
      location: '世纪城地铁站集合',
      user: {
        nickname: '小兔快快',
        avatar: '/static/images/login-female-default.png',
      },
    },
  ]
}

// 跳转到拼房拼车详情
const toCompanionDetail = (id) => {
  uni.navigateTo({
    url: '/pages/users/partner_details/index?id=' + id,
  })
}

function handleClick(e) {
  console.log(e)
}
function onChange(e) {
  console.log(e)
}

// 加载banner
const loadBanner = async () => {
  const [res, err] = await getBanners()
  if (res) {
    swiperList.value = res.data.map((item) => item.img)
  }
}

// 加载热门活动
const loadHotActivities = async () => {
  // 使用测试数据
  hotActivities.value = [
    {
      id: 1,
      title: '2025重庆(长嘉汇)半程马拉松',
      address: '稻城亚丁+四姑娘山+色达',
      add_time_date: '8月26日 周一',
      image_input: ['/static/images/banner.png'],
      status: 1,
    },
    {
      id: 2,
      title: '2025大邑安仁半程马拉松',
      address: '成都市天府新区',
      add_time_date: '8月28日 周三',
      image_input: ['/static/images/bg1.png'],
      status: 1,
    },
  ]
}

// 加载热门话题
const loadHotTopics = async () => {
  // 使用测试数据
  hotTopics.value = [
    {
      id: 1,
      title: '请问特斯拉4S店贴的3M膜怎么样？',
      image_input: ['/static/images/banner.png'],
      user: {
        nickname: '美丽人生',
        avatar: '/static/images/login-female-default.png',
      },
      add_time_date: '2小时前',
    },
    {
      id: 2,
      title: '贴车衣有必要吗？',
      image_input: ['/static/images/bg1.png'],
      user: {
        nickname: '美丽人生',
        avatar: '/static/images/login-male-default.png',
      },
      add_time_date: '5小时前',
    },
  ]
}

// 跳转方法
const toActivityList = () => {
  uni.navigateTo({
    url: '/pages/activity/activity-list',
  })
}

const toActivityDetail = (id) => {
  uni.navigateTo({
    url: '/pages/shequ/detail?id=' + id,
  })
}

const toTopicList = () => {
  uni.navigateTo({
    url: '/pages/shequ/index',
  })
}

const toTopicDetail = (id) => {
  uni.navigateTo({
    url: '/pages/shequ/detail?id=' + id,
  })
}

const toPost = () => {
  uni.navigateTo({
    url: '/pages/shequ/index',
  })
}

loadUserList()
loadBanner()
loadHotActivities()
loadHotTopics()
loadCompanionList()

const onSwiperChange = (e) => {
  self.current = e.detail.current
  if (e.detail.current === self.userList.length - 1) {
    loadUserList()
  }
}

// 监听滚动高度
onPageScroll((res) => {
  uni.$emit('onPageScroll', res.scrollTop)
})
</script>

<style lang="scss" scoped>
.wrapper {
  position: relative;
  min-height: 100vh;
  padding-bottom: 40rpx;
  background-image: linear-gradient(0deg, #f0f0f0 0%, #e9fbc5 99%);
  background-repeat: no-repeat;
  background-size: 100% 300rpx;
}

.cover-wrap {
  width: 100%;
  margin-top: 50rpx;
}

.swiper {
  height: 930rpx;
}

.swiper-item {
  padding: 0 10rpx;
  border-radius: 0 0 20rpx 20rpx;
  transition: all 0.2s ease-in-out;
  transform: scale(0.96);

  .cover-img {
    position: relative;

    .image {
      width: 100%;
      height: 670rpx;
      border-radius: 20rpx 20rpx 0 0;
    }

    .heart {
      position: absolute;
      right: 30rpx;
      bottom: 50rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 58rpx;
      height: 58rpx;
      background: linear-gradient(-31deg, #ff94b2, #f9225e);
      border-radius: 50%;
      box-shadow: 0rpx 3rpx 12rpx 1rpx rgba(0, 0, 0, 0.31);

      .icon-heart {
        width: 30rpx;
        height: 27rpx;
      }
    }
  }

  .cover-info {
    position: relative;
    padding: 32rpx 28rpx 28rpx;
    background-color: #fff;
    border-radius: 0 0 20rpx 20rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);

    .avatar {
      position: absolute;
      top: -96rpx;
      left: 24rpx;
      width: 106rpx;
      height: 106rpx;
      overflow: hidden;
      border: 3px solid #ffffff;
      border-radius: 8rpx;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);

      .image {
        width: 100%;
        height: 100%;
      }
    }

    .nickname-wrap {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .nickname {
        font-size: 34rpx;
        font-weight: bold;
        color: #303030;
      }

      .real-name-status {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8rpx 12rpx;
        margin-left: 20rpx;
        font-size: 20rpx;
        color: #ffffff;
        border-radius: 36rpx;

        .icon {
          width: 22rpx;
          height: 22rpx;
          margin-right: 6rpx;
        }
      }

      .yes {
        background: linear-gradient(-31deg, #ff94b2, #f9225e);
      }

      .level-img {
        display: flex;
        column-gap: 8rpx;
        align-items: center;
        margin-left: 10rpx;

        .image {
          width: 140rpx;
          height: 36rpx;
        }
      }

      .desc {
        font-size: 20rpx;
        font-weight: 500;
        color: #ff225e;
      }
    }

    .tags {
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      margin-top: 36rpx;

      .tag {
        &__item {
          display: flex;
          align-items: center;
          margin-right: 30rpx;
          margin-bottom: 15rpx;
          font-size: 24rpx;
          font-weight: 400;
          color: #303030;
        }
      }

      .tag__icon__box {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 38rpx;
        height: 38rpx;
        margin-right: 20rpx;
        background-color: #fdf3f4;
        border-radius: 50%;
      }

      .tag__icon__box-nan {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 38rpx;
        height: 38rpx;
        margin-right: 20rpx;
        border-radius: 50%;
      }
    }
  }
}

.swiper-item-active {
  transform: scale(1);
}
.menus {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #ffffff;
  border-radius: 30rpx;
  padding: 40rpx 30rpx;
  .menu {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .icon {
      width: 70rpx;
      height: 70prx;
      border-radius: 50%;
    }
    .name {
      font-weight: 500;
      font-size: 26rpx;
      color: #1a1a1a;
      margin-top: 10rpx;
    }
  }
}

// 位置选择样式
.location-section {
  padding: 40rpx 30rpx 10rpx;
  .location-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .location-text {
      display: flex;
      align-items: center;

      .location-label {
        font-size: 28rpx;
        color: #333;
        margin-right: 20rpx;
      }

      .location-picker {
        display: flex;
        align-items: center;

        .location-value {
          font-size: 28rpx;
          color: #666;
          margin-right: 8rpx;
        }
      }
    }

    .location-btn {
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      background: #f5f5f5;
      border-radius: 20rpx;

      .location-btn-text {
        font-size: 24rpx;
        color: #666;
        margin-left: 8rpx;
      }
    }
  }
}

// 拼房拼车板块样式
.companion-section {
  padding: 20rpx;

  .companion-tabs {
    display: flex;
    gap: 60rpx;
    padding: 0 8rpx 20rpx;
    margin-bottom: 16rpx;

    .tab {
      position: relative;
      font-size: 32rpx;
      font-weight: 700;
      color: #6f7686;
      padding: 20rpx 8rpx 12rpx;
    }
    .tab.active {
      color: #1a1a1a;
    }
    .tab.active::after {
      content: '';
      position: absolute;
      left: 6rpx;
      bottom: 6rpx;
      width: 120rpx;
      height: 26rpx;
      background: #c5f355;
      border-radius: 20rpx;
      z-index: -1;
    }
  }

  .companion-card {
    background: #fff;
    border-radius: 20rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .user-info {
        display: flex;
        align-items: center;

        .user-avatar {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          margin-right: 16rpx;
        }

        .user-details {
          display: flex;
          align-items: center;
          .user-name {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
            margin-right: 16rpx;
          }

          .user-location {
            display: flex;
            align-items: center;
            .icon {
              width: 24rpx;
              height: 26rpx;
            }

            .location-text {
              font-size: 24rpx;
              color: #999;
              margin-left: 8rpx;
            }
          }
        }
      }

      .post-btn-small {
        display: flex;
        align-items: center;
        padding: 8rpx 16rpx;
        background: #c5f355;
        border-radius: 20rpx;

        .post-text {
          font-size: 24rpx;
          color: #333;
          margin-left: 8rpx;
        }
      }
    }

    .card-content {
      .content-title {
        font-weight: 400;
        font-size: 26rpx;
        color: #333333;
        margin-bottom: 16rpx;
      }
      .avatar {
        width: 148rpx;
        height: 148rpx;
        border-radius: 16rpx;
        margin-right: 12rpx;
      }

      .content-tags {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .tag {
          padding: 6rpx 12rpx;
          border-radius: 16rpx;
          font-size: 22rpx;
          margin-right: 12rpx;

          &.price-tag {
            font-weight: 400;
            font-size: 28rpx;
            color: #ed1212;
            &::before {
              content: '￥';
              font-size: 22rpx;
            }
          }

          &.type-tag {
            background: #c5f355;
            border-radius: 6rpx;
          }

          &.gender-tag {
            border-radius: 6rpx;
            border: 1px solid #1a1a1a;
            font-weight: 400;
            font-size: 22rpx;
            color: #1a1a1a;
          }
        }
      }

      .content-info {
        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;
          .icon {
            width: 28rpx;
            height: 28rpx;
          }

          .info-text {
            font-size: 24rpx;
            color: #666;
            margin-left: 8rpx;
          }
        }
      }
    }
  }
}

// 热门板块样式
.hot-section {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #303030;
    }

    .more-btn {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #999;

      .iconfont {
        margin-left: 8rpx;
        font-size: 20rpx;
      }
    }
  }
}

// 活动列表样式
.activity-list {
  padding: 40rpx 20rpx 20rpx;
  white-space: nowrap;
  background-color: white;
  width: 100%;
  .activity-item {
    display: inline-block;
    width: 460rpx;
    .activity-image-wrapper {
      position: relative;
      margin-right: 20rpx;
      .activity-image {
        width: 100%;
        height: 200rpx;
        border-radius: 16rpx;
        margin-bottom: 20rpx;
      }
      .activity-status {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        background: #000000;
        border-radius: 11rpx 11rpx 0rpx 0rpx;
        opacity: 0.68;
        font-weight: 300;
        font-size: 20rpx;
        color: #c5f355;
        text-align: center;
        padding: 10rpx 0;
      }
    }

    .activity-info {
      flex: 1;
      .activity-title {
        font-weight: 300;
        font-size: 24rpx;
        color: #1a1a1a;
      }
    }
  }
}

// 话题列表样式
.topic-list {
  .topic-item {
    display: flex;
    padding: 20rpx;
    margin-bottom: 20rpx;
    background: #fff;

    .topic-image-wrapper {
      margin-right: 20rpx;

      .topic-image {
        width: 148rpx;
        height: 148rpx;
        border-radius: 20rpx;
      }
    }

    .topic-info {
      flex: 1;

      .topic-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #303030;
        line-height: 1.4;
        margin-bottom: 40rpx;
      }
      .topic-author {
        display: flex;
        align-items: center;
        .author-avatar {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          margin-right: 12rpx;
        }

        .author-name {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }
}

// 发帖按钮样式
.post-btn {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 136rpx;
  height: 68rpx;
  background: #c5f355;
  border-radius: 34rpx;
  z-index: 100;
  column-gap: 10rpx;
  .iconfont {
    margin-right: 8rpx;
    font-size: 24rpx;
    color: #333;
  }

  font-size: 24rpx;
  color: #333;
}
</style>
